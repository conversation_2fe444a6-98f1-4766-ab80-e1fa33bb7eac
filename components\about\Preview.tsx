"use client"; // Assuming this component needs client-side interactivity (matter-js)

import React from 'react';
import { Gravity, MatterBody } from '@/components/ui/gravity'; // Use alias path

// It's good practice to define props if needed, even if empty for now
interface PreviewProps {}

const Preview: React.FC<PreviewProps> = () => {
  // Define colors for value blocks (can be customized)
  const valueColors = {
    Honesty: "bg-blue-500",
    Responsibility: "bg-green-500",
    ClientCare: "bg-pink-500",
    Trustworthiness: "bg-purple-500",
  };

  return (
    // Changed w-full to max-w-4xl mx-auto, reduced min-h
    <section className="max-w-4xl mx-auto min-h-[450px] flex flex-col relative font-azeretMono mb-16 md:mb-24 bg-gray-100 rounded-lg py-10 px-4 overflow-hidden">
      {/* Adjusted text colors for light theme */}
      <h2 className="text-3xl md:text-4xl font-semibold mb-8 text-center text-gray-900">
      These values are the base of everything we do.
      </h2>
      {/* Removed the "fancy" and "components made with" text */}
      {/* Gravity container needs relative positioning for absolute children, reduced min-h */}
      <div className="relative w-full flex-grow min-h-[350px]">
        <Gravity
          gravity={{ x: 0, y: 1 }}
          className="w-full h-full"
          resetOnResize={false}
          addTopWall={true} // Add top wall to prevent escape
          debug={false}
        >
          {/* Value Blocks */}
          <MatterBody
            matterBodyOptions={{
              friction: 0.8, // Increased friction to prevent sliding
              restitution: 0.2, // Slightly more bounce for better physics
              density: 0.005, // Further increased density for better collision detection
              frictionAir: 0.01, // Add air resistance to slow down movement
              inertia: Infinity, // Prevent rotation during collisions for more stable behavior
              collisionFilter: {
                group: 0, // Default collision group
                category: 0x0001, // Collision category
                mask: 0xFFFF // Collides with everything
              }
            }}
            x="40%"
            y="10%" // Moved down slightly from top
          >
            <div className={`text-lg sm:text-xl md:text-2xl ${valueColors.Honesty} text-white rounded-lg shadow-md hover:cursor-grab px-6 py-3`}>
              Honesty
            </div>
          </MatterBody>
          <MatterBody
            matterBodyOptions={{
              friction: 0.8,
              restitution: 0.2,
              density: 0.005,
              frictionAir: 0.01,
              inertia: Infinity,
              collisionFilter: {
                group: 0,
                category: 0x0001,
                mask: 0xFFFF
              }
            }}
            x="60%"
            y="15%" // Moved down slightly from top
          >
            <div className={`text-lg sm:text-xl md:text-2xl ${valueColors.Responsibility} text-white rounded-lg shadow-md hover:cursor-grab px-6 py-3`}>
              Responsibility
            </div>
          </MatterBody>
          <MatterBody
            matterBodyOptions={{
              friction: 0.8,
              restitution: 0.2,
              density: 0.005,
              frictionAir: 0.01,
              inertia: Infinity,
              collisionFilter: {
                group: 0,
                category: 0x0001,
                mask: 0xFFFF
              }
            }}
            x="30%"
            y="20%" // Moved down slightly from top
          >
            <div className={`text-lg sm:text-xl md:text-2xl ${valueColors.ClientCare} text-white rounded-lg shadow-md hover:cursor-grab px-6 py-3`}>
              Client Care
            </div>
          </MatterBody>
          <MatterBody
            matterBodyOptions={{
              friction: 0.8,
              restitution: 0.2,
              density: 0.005,
              frictionAir: 0.01,
              inertia: Infinity,
              collisionFilter: {
                group: 0,
                category: 0x0001,
                mask: 0xFFFF
              }
            }}
            x="70%"
            y="10%" // Moved down slightly from top
          >
            <div className={`text-lg sm:text-xl md:text-2xl ${valueColors.Trustworthiness} text-white rounded-lg shadow-md hover:cursor-grab px-6 py-3`}>
              Trustworthiness
            </div>
          </MatterBody>
        </Gravity>
      </div>
    </section>
  );
};

// Export using named export
export { Preview };
